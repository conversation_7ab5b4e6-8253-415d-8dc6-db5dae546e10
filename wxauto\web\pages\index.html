<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <style>
        body {
            margin: 0;
            padding: 0;
        }

        .layout-container {
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container class="layout-container">
            <!-- 侧边栏 -->
            <el-aside width="200px">
                <div style="padding: 20px; text-align: center; border-bottom: 1px solid #e4e7ed;">
                    <h3>Matrix Admin</h3>
                </div>

                <el-menu
                    :default-active="activeMenu"
                    @select="handleMenuSelect">

                    <el-menu-item index="home">
                        <el-icon><House /></el-icon>
                        <span>仪表盘</span>
                    </el-menu-item>

                    <el-sub-menu index="data">
                        <template #title>
                            <el-icon><DataAnalysis /></el-icon>
                            <span>数据管理</span>
                        </template>
                    </el-sub-menu>

                    <el-sub-menu index="payment">
                        <template #title>
                            <el-icon><CreditCard /></el-icon>
                            <span>支付管理</span>
                        </template>
                        <el-menu-item index="payment-channel">
                            <el-icon><Setting /></el-icon>
                            <span>支付渠道管理</span>
                        </el-menu-item>
                        <el-menu-item index="payment-record">
                            <el-icon><Document /></el-icon>
                            <span>支付记录</span>
                        </el-menu-item>
                        <el-menu-item index="refund">
                            <el-icon><RefreshLeft /></el-icon>
                            <span>退款管理</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-sub-menu index="system">
                        <template #title>
                            <el-icon><Setting /></el-icon>
                            <span>系统管理</span>
                        </template>
                    </el-sub-menu>

                    <el-sub-menu index="permission">
                        <template #title>
                            <el-icon><Lock /></el-icon>
                            <span>权限管理</span>
                        </template>
                    </el-sub-menu>

                    <el-sub-menu index="basic">
                        <template #title>
                            <el-icon><Grid /></el-icon>
                            <span>基础管理</span>
                        </template>
                    </el-sub-menu>

                    <el-sub-menu index="zhenmama">
                        <template #title>
                            <el-icon><User /></el-icon>
                            <span>禅妈妈</span>
                        </template>
                        <el-menu-item index="zhenmama-expert-data">
                            <el-icon><DataAnalysis /></el-icon>
                            <span>达人数据提取</span>
                        </el-menu-item>
                    </el-sub-menu>
                </el-menu>
            </el-aside>

            <!-- 主内容区 -->
            <el-container>
                <el-header style="border-bottom: 1px solid #e4e7ed; display: flex; align-items: center; justify-content: space-between;">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.name">
                            {{ item.name }}
                        </el-breadcrumb-item>
                    </el-breadcrumb>

                    <el-dropdown>
                        <span style="cursor: pointer;">
                            <el-avatar :size="32"></el-avatar>
                            <span style="margin-left: 8px;">管理员</span>
                            <el-icon><ArrowDown /></el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item>个人设置</el-dropdown-item>
                                <el-dropdown-item divided>退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </el-header>

                <el-main>
                    <!-- 首页内容 -->
                    <div v-if="currentPage === 'home'">
                        <h2>仪表盘</h2>
                        <p>欢迎使用后台管理系统</p>
                    </div>

                    <!-- 达人数据提取页面 -->
                    <div v-else-if="currentPage === 'zhenmama-expert-data'">
                        <el-card>
                            <template #header>
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <span>达人数据提取</span>
                                    <el-button type="primary" @click="startExtraction">
                                        <el-icon><Download /></el-icon>
                                        开始提取
                                    </el-button>
                                </div>
                            </template>

                            <el-form :model="extractForm" label-width="120px">
                                <el-row :gutter="20">
                                    <el-col :span="12">
                                        <el-form-item label="提取类型">
                                            <el-select v-model="extractForm.type" placeholder="请选择提取类型" style="width: 100%;">
                                                <el-option label="全部达人" value="all"></el-option>
                                                <el-option label="活跃达人" value="active"></el-option>
                                                <el-option label="新增达人" value="new"></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="数据范围">
                                            <el-date-picker
                                                v-model="extractForm.dateRange"
                                                type="daterange"
                                                range-separator="至"
                                                start-placeholder="开始日期"
                                                end-placeholder="结束日期"
                                                style="width: 100%;">
                                            </el-date-picker>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-form-item label="提取字段">
                                    <el-checkbox-group v-model="extractForm.fields">
                                        <el-checkbox label="nickname">昵称</el-checkbox>
                                        <el-checkbox label="avatar">头像</el-checkbox>
                                        <el-checkbox label="fans">粉丝数</el-checkbox>
                                        <el-checkbox label="posts">发帖数</el-checkbox>
                                        <el-checkbox label="engagement">互动率</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-form>

                            <el-divider></el-divider>

                            <h3>提取历史</h3>
                            <el-table :data="extractHistory" style="width: 100%; margin-top: 15px;">
                                <el-table-column prop="date" label="提取时间" width="180"></el-table-column>
                                <el-table-column prop="type" label="类型" width="120"></el-table-column>
                                <el-table-column prop="count" label="数量" width="100"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template #default="scope">
                                        <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作">
                                    <template #default="scope">
                                        <el-button size="small" @click="downloadData(scope.row)">下载</el-button>
                                        <el-button size="small" type="danger" @click="deleteRecord(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;

        const app = createApp({
            setup() {
                const activeMenu = ref('home');
                const currentPage = ref('home');

                const extractForm = ref({
                    type: 'all',
                    dateRange: '',
                    fields: ['nickname', 'avatar', 'fans']
                });

                const extractHistory = ref([
                    {
                        date: '2024-01-20 10:30:00',
                        type: '全部达人',
                        count: 156,
                        status: '成功'
                    },
                    {
                        date: '2024-01-19 15:20:00',
                        type: '活跃达人',
                        count: 89,
                        status: '成功'
                    },
                    {
                        date: '2024-01-18 09:15:00',
                        type: '新增达人',
                        count: 23,
                        status: '失败'
                    }
                ]);

                const breadcrumbs = computed(() => {
                    const routes = {
                        'home': [{ name: '仪表盘' }],
                        'zhenmama-expert-data': [{ name: '禅妈妈' }, { name: '达人数据提取' }]
                    };
                    return routes[currentPage.value] || [];
                });

                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                    currentPage.value = index;
                };

                const startExtraction = () => {
                    ElMessage.success('开始提取达人数据...');
                };

                const downloadData = (row) => {
                    ElMessage.info(`下载 ${row.date} 的数据`);
                };

                const deleteRecord = (row) => {
                    ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        ElMessage.success('删除成功');
                    });
                };

                return {
                    activeMenu,
                    currentPage,
                    extractForm,
                    extractHistory,
                    breadcrumbs,
                    handleMenuSelect,
                    startExtraction,
                    downloadData,
                    deleteRecord
                };
            }
        });

        // 注册 Element Plus Icons
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>