<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信自动化管理后台</title>

    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- Element Plus Icons -->
    <link rel="stylesheet" href="https://unpkg.com/@element-plus/icons-vue/dist/index.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, <PERSON>xy<PERSON>, <PERSON>bu<PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .admin-container {
            height: 100vh;
            display: flex;
            background: #f5f7fa;
        }

        .sidebar {
            width: 250px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1000;
        }

        .sidebar.collapsed {
            width: 64px;
        }

        .sidebar-header {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            color: #fff;
            font-size: 20px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
            margin: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .content-header {
            height: 60px;
            background: #fff;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .content-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafbfc;
        }

        .page-transition {
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .el-menu {
            border-right: none !important;
            background: transparent !important;
        }

        .el-menu-item, .el-sub-menu__title {
            color: rgba(255, 255, 255, 0.8) !important;
            border-radius: 8px !important;
            margin: 4px 12px !important;
            width: calc(100% - 24px) !important;
        }

        .el-menu-item:hover, .el-sub-menu__title:hover {
            background: rgba(255, 255, 255, 0.1) !important;
            color: #fff !important;
        }

        .el-menu-item.is-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: #fff !important;
        }

        .el-sub-menu .el-menu-item {
            background: rgba(0, 0, 0, 0.1) !important;
            margin-left: 20px !important;
            width: calc(100% - 44px) !important;
        }

        .toggle-btn {
            position: absolute;
            top: 15px;
            right: -15px;
            width: 30px;
            height: 30px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background: #337ecc;
            transform: scale(1.1);
        }

        .breadcrumb {
            margin-bottom: 20px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .welcome-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            color: #333;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(255, 154, 158, 0.3);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="admin-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ collapsed: isCollapsed }">
                <button class="toggle-btn" @click="toggleSidebar">
                    <i :class="isCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
                </button>

                <div class="sidebar-header">
                    <a href="#" class="logo" v-show="!isCollapsed">微信管理后台</a>
                    <a href="#" class="logo" v-show="isCollapsed">WX</a>
                </div>

                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    @select="handleMenuSelect"
                    text-color="rgba(255, 255, 255, 0.8)"
                    active-text-color="#fff">

                    <el-menu-item index="home">
                        <el-icon><House /></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-sub-menu index="zhenmama">
                        <template #title>
                            <el-icon><User /></el-icon>
                            <span>禅妈妈</span>
                        </template>
                        <el-menu-item index="zhenmama-expert-data">
                            <el-icon><DataAnalysis /></el-icon>
                            <span>达人数据提取</span>
                        </el-menu-item>
                    </el-sub-menu>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <div class="content-header">
                    <el-breadcrumb separator="/" class="breadcrumb">
                        <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.name">
                            {{ item.name }}
                        </el-breadcrumb-item>
                    </el-breadcrumb>

                    <div style="margin-left: auto;">
                        <el-dropdown>
                            <span class="el-dropdown-link">
                                <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
                                <span style="margin-left: 8px;">管理员</span>
                                <el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>个人设置</el-dropdown-item>
                                    <el-dropdown-item divided>退出登录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>

                <div class="content-body">
                    <div class="page-transition" :key="currentPage">
                        <!-- 首页内容 -->
                        <div v-if="currentPage === 'home'">
                            <div class="welcome-card animate__animated animate__fadeInDown">
                                <h1 style="margin-bottom: 10px; font-size: 28px;">欢迎使用微信自动化管理后台</h1>
                                <p style="font-size: 16px; opacity: 0.8;">高效管理您的微信自动化任务</p>
                            </div>

                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-card class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                                        <div style="display: flex; align-items: center;">
                                            <el-icon size="40" style="margin-right: 15px;"><ChatDotRound /></el-icon>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">1,234</div>
                                                <div style="opacity: 0.8;">消息总数</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;">
                                        <div style="display: flex; align-items: center;">
                                            <el-icon size="40" style="margin-right: 15px;"><User /></el-icon>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">567</div>
                                                <div style="opacity: 0.8;">联系人数</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                                        <div style="display: flex; align-items: center;">
                                            <el-icon size="40" style="margin-right: 15px;"><TrendCharts /></el-icon>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">89</div>
                                                <div style="opacity: 0.8;">自动化任务</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>

                                <el-col :span="6">
                                    <el-card class="stats-card animate__animated animate__fadeInUp" style="animation-delay: 0.4s; background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); color: #333;">
                                        <div style="display: flex; align-items: center;">
                                            <el-icon size="40" style="margin-right: 15px;"><SuccessFilled /></el-icon>
                                            <div>
                                                <div style="font-size: 24px; font-weight: bold;">98.5%</div>
                                                <div style="opacity: 0.8;">成功率</div>
                                            </div>
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20" style="margin-top: 20px;">
                                <el-col :span="12">
                                    <el-card class="animate__animated animate__fadeInLeft" style="animation-delay: 0.5s;">
                                        <template #header>
                                            <div style="display: flex; align-items: center;">
                                                <el-icon style="margin-right: 8px;"><TrendCharts /></el-icon>
                                                <span>最近活动</span>
                                            </div>
                                        </template>
                                        <el-timeline>
                                            <el-timeline-item timestamp="2024-01-20 10:30" placement="top">
                                                <el-card>
                                                    <h4>数据提取任务完成</h4>
                                                    <p>成功提取了 156 条达人数据</p>
                                                </el-card>
                                            </el-timeline-item>
                                            <el-timeline-item timestamp="2024-01-20 09:15" placement="top">
                                                <el-card>
                                                    <h4>自动回复设置更新</h4>
                                                    <p>更新了关键词自动回复规则</p>
                                                </el-card>
                                            </el-timeline-item>
                                            <el-timeline-item timestamp="2024-01-20 08:00" placement="top">
                                                <el-card>
                                                    <h4>系统启动</h4>
                                                    <p>微信自动化系统正常启动</p>
                                                </el-card>
                                            </el-timeline-item>
                                        </el-timeline>
                                    </el-card>
                                </el-col>

                                <el-col :span="12">
                                    <el-card class="animate__animated animate__fadeInRight" style="animation-delay: 0.6s;">
                                        <template #header>
                                            <div style="display: flex; align-items: center;">
                                                <el-icon style="margin-right: 8px;"><Bell /></el-icon>
                                                <span>系统通知</span>
                                            </div>
                                        </template>
                                        <el-alert
                                            title="系统运行正常"
                                            type="success"
                                            :closable="false"
                                            style="margin-bottom: 15px;">
                                        </el-alert>
                                        <el-alert
                                            title="今日已处理消息 1,234 条"
                                            type="info"
                                            :closable="false"
                                            style="margin-bottom: 15px;">
                                        </el-alert>
                                        <el-alert
                                            title="建议定期备份数据"
                                            type="warning"
                                            :closable="false">
                                        </el-alert>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <!-- 达人数据提取页面 -->
                        <div v-else-if="currentPage === 'zhenmama-expert-data'">
                            <el-card class="animate__animated animate__fadeInDown">
                                <template #header>
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <div style="display: flex; align-items: center;">
                                            <el-icon style="margin-right: 8px;"><DataAnalysis /></el-icon>
                                            <span>达人数据提取</span>
                                        </div>
                                        <el-button type="primary" @click="startExtraction">
                                            <el-icon><Download /></el-icon>
                                            开始提取
                                        </el-button>
                                    </div>
                                </template>

                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <el-card shadow="hover">
                                            <el-statistic title="今日提取" :value="156" suffix="条">
                                                <template #prefix>
                                                    <el-icon style="vertical-align: -0.125em"><TrendCharts /></el-icon>
                                                </template>
                                            </el-statistic>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-card shadow="hover">
                                            <el-statistic title="总计提取" :value="12567" suffix="条">
                                                <template #prefix>
                                                    <el-icon style="vertical-align: -0.125em"><DataAnalysis /></el-icon>
                                                </template>
                                            </el-statistic>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="8">
                                        <el-card shadow="hover">
                                            <el-statistic title="成功率" :value="98.5" suffix="%">
                                                <template #prefix>
                                                    <el-icon style="vertical-align: -0.125em"><SuccessFilled /></el-icon>
                                                </template>
                                            </el-statistic>
                                        </el-card>
                                    </el-col>
                                </el-row>

                                <el-divider></el-divider>

                                <el-form :model="extractForm" label-width="120px" style="margin-top: 20px;">
                                    <el-row :gutter="20">
                                        <el-col :span="12">
                                            <el-form-item label="提取类型">
                                                <el-select v-model="extractForm.type" placeholder="请选择提取类型" style="width: 100%;">
                                                    <el-option label="全部达人" value="all"></el-option>
                                                    <el-option label="活跃达人" value="active"></el-option>
                                                    <el-option label="新增达人" value="new"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="数据范围">
                                                <el-date-picker
                                                    v-model="extractForm.dateRange"
                                                    type="daterange"
                                                    range-separator="至"
                                                    start-placeholder="开始日期"
                                                    end-placeholder="结束日期"
                                                    style="width: 100%;">
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>

                                    <el-form-item label="提取字段">
                                        <el-checkbox-group v-model="extractForm.fields">
                                            <el-checkbox label="nickname">昵称</el-checkbox>
                                            <el-checkbox label="avatar">头像</el-checkbox>
                                            <el-checkbox label="fans">粉丝数</el-checkbox>
                                            <el-checkbox label="posts">发帖数</el-checkbox>
                                            <el-checkbox label="engagement">互动率</el-checkbox>
                                        </el-checkbox-group>
                                    </el-form-item>
                                </el-form>

                                <el-divider></el-divider>

                                <div style="margin-top: 20px;">
                                    <h3 style="margin-bottom: 15px;">提取历史</h3>
                                    <el-table :data="extractHistory" style="width: 100%">
                                        <el-table-column prop="date" label="提取时间" width="180"></el-table-column>
                                        <el-table-column prop="type" label="类型" width="120"></el-table-column>
                                        <el-table-column prop="count" label="数量" width="100"></el-table-column>
                                        <el-table-column prop="status" label="状态" width="100">
                                            <template #default="scope">
                                                <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                                                    {{ scope.row.status }}
                                                </el-tag>
                                            </template>
                                        </el-table-column>
                                        <el-table-column label="操作">
                                            <template #default="scope">
                                                <el-button size="small" @click="downloadData(scope.row)">下载</el-button>
                                                <el-button size="small" type="danger" @click="deleteRecord(scope.row)">删除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Element Plus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <script>
        const { createApp, ref, computed } = Vue;

        const app = createApp({
            setup() {
                const isCollapsed = ref(false);
                const activeMenu = ref('home');
                const currentPage = ref('home');

                const extractForm = ref({
                    type: 'all',
                    dateRange: '',
                    fields: ['nickname', 'avatar', 'fans']
                });

                const extractHistory = ref([
                    {
                        date: '2024-01-20 10:30:00',
                        type: '全部达人',
                        count: 156,
                        status: '成功'
                    },
                    {
                        date: '2024-01-19 15:20:00',
                        type: '活跃达人',
                        count: 89,
                        status: '成功'
                    },
                    {
                        date: '2024-01-18 09:15:00',
                        type: '新增达人',
                        count: 23,
                        status: '失败'
                    }
                ]);

                const breadcrumbs = computed(() => {
                    const routes = {
                        'home': [{ name: '首页' }],
                        'zhenmama-expert-data': [{ name: '禅妈妈' }, { name: '达人数据提取' }]
                    };
                    return routes[currentPage.value] || [];
                });

                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const handleMenuSelect = (index) => {
                    activeMenu.value = index;
                    currentPage.value = index;
                };

                const startExtraction = () => {
                    ElMessage.success('开始提取达人数据...');
                };

                const downloadData = (row) => {
                    ElMessage.info(`下载 ${row.date} 的数据`);
                };

                const deleteRecord = (row) => {
                    ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        ElMessage.success('删除成功');
                    });
                };

                return {
                    isCollapsed,
                    activeMenu,
                    currentPage,
                    extractForm,
                    extractHistory,
                    breadcrumbs,
                    toggleSidebar,
                    handleMenuSelect,
                    startExtraction,
                    downloadData,
                    deleteRecord
                };
            }
        });

        // 注册 Element Plus Icons
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }

        app.use(ElementPlus).mount('#app');
    </script>
</body>
</html>