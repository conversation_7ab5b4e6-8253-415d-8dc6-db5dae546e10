import webview
import os





def main():
    print("启动智慧数据洞察平台...")

    # 获取web目录和HTML文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    web_dir = os.path.join(current_dir, "web")
    html_path = os.path.join(web_dir, "pages", "index.html")

    print(f"Web目录: {web_dir}")
    print(f"HTML文件路径: {html_path}")

    # 确保文件存在
    if not os.path.exists(html_path):
        print(f"HTML文件不存在: {html_path}")
        return
    else:
        print("HTML文件存在，准备启动...")

    # 创建webview窗口
    webview.create_window(
        title="智慧数据洞察平台",
        url=html_path,
        width=1400,
        height=900,
        min_size=(1000, 700),
        resizable=True,
        maximized=False
    )

    print("启动webview...")
    # 启动webview（关闭调试模式）
    webview.start(debug=False)
    print("应用已关闭")


if __name__ == "__main__":
    main()