/* 后台管理系统样式 - index.css */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
}

.admin-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 背景装饰 */
.background-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation: float 8s ease-in-out infinite reverse;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation: float 10s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 50px;
    color: white;
}

.logo-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.logo-icon {
    font-size: 3rem;
    margin-right: 15px;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.title {
    font-size: 3rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    font-weight: 300;
}

/* 主要内容 */
.main-content {
    margin-bottom: 50px;
}

/* 功能卡片网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 50px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.card-icon {
    text-align: center;
    margin-bottom: 20px;
}

.card-icon i {
    font-size: 3rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 15px;
    text-align: center;
}

.card-description {
    color: #718096;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: center;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-status {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.card-status.active {
    background: #c6f6d5;
    color: #22543d;
}

.card-status.coming-soon {
    background: #fed7d7;
    color: #742a2a;
}

.card-status.beta {
    background: #bee3f8;
    color: #2a4365;
}

.card-arrow {
    color: #a0aec0;
    transition: all 0.3s ease;
}

.feature-card:hover .card-arrow {
    color: #667eea;
    transform: translateX(5px);
}

/* 统计信息 */
.stats-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-title {
    text-align: center;
    color: #2d3748;
    margin-bottom: 30px;
    font-size: 1.8rem;
    font-weight: 600;
}

.stats-title i {
    margin-right: 10px;
    color: #667eea;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f7fafc;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
}

.stat-icon {
    margin-right: 15px;
}

.stat-icon i {
    font-size: 2rem;
    color: #667eea;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    color: #718096;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 底部 */
.footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .logo-icon {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1.1rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .logo-section {
        flex-direction: column;
    }
    
    .logo-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .feature-card {
        padding: 20px;
    }
    
    .stats-section {
        padding: 20px;
    }
    
    .stat-item {
        padding: 15px;
    }
}
