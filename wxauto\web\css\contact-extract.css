/* 联系人数据提取页面样式 */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
}

.admin-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航样式 */
.admin-header {
    background: #409eff;
    color: white;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15);
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo-icon {
    margin-right: 12px;
    color: #ffd04b;
}

.logo-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-right: 12px;
}

.user-section {
    display: flex;
    align-items: center;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: #f5f7fa;
}

/* 页面头部 */
.page-header {
    margin-bottom: 24px;
}

.breadcrumb-link {
    cursor: pointer;
    color: #409eff;
}

.breadcrumb-link:hover {
    color: #66b1ff;
}

.page-title {
    margin-top: 16px;
}

.page-title h1 {
    font-size: 2rem;
    color: #303133;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.page-title h1 i {
    margin-right: 12px;
    color: #409eff;
}

.page-title p {
    color: #909399;
    font-size: 1rem;
}

/* 内容容器 */
.content-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* 卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-header i {
    margin-right: 8px;
    color: #409eff;
}

/* 上传卡片 */
.upload-card {
    margin-bottom: 24px;
}

.upload-demo {
    width: 100%;
}

.upload-content {
    text-align: center;
    padding: 40px 20px;
}

.upload-icon {
    font-size: 4rem;
    color: #409eff;
    margin-bottom: 16px;
}

.upload-text p {
    margin: 8px 0;
    color: #606266;
}

.upload-text em {
    color: #409eff;
    font-style: normal;
}

.upload-tip {
    font-size: 0.9rem;
    color: #909399;
}

.file-info {
    margin-top: 20px;
}

.upload-actions {
    margin-top: 16px;
    text-align: center;
}

.upload-actions .el-button {
    margin: 0 8px;
}

/* 结果卡片 */
.result-card {
    margin-bottom: 24px;
}

/* 统计行 */
.stats-row {
    margin-bottom: 24px;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    transition: all 0.3s ease;
}

.stat-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 12px 0 rgba(64,158,255,.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    background: linear-gradient(135deg, #409eff, #66b1ff);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.stat-icon i {
    font-size: 1.5rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #303133;
    line-height: 1;
}

.stat-label {
    color: #909399;
    font-size: 0.9rem;
    margin-top: 4px;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ebeef5;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
}

.table-header h3 {
    color: #303133;
    font-size: 1.2rem;
    margin: 0;
}

/* Element Plus 组件自定义样式 */
.el-upload-dragger {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.el-upload-dragger:hover {
    border-color: #409eff;
    background: #f0f9ff;
}

.el-card {
    border-radius: 12px;
    border: 1px solid #ebeef5;
}

.el-card__header {
    background: #fafbfc;
    border-bottom: 1px solid #ebeef5;
}

.el-table {
    border-radius: 8px;
    overflow: hidden;
}

.el-table th {
    background: #fafbfc;
    color: #606266;
    font-weight: 600;
}

.el-button {
    border-radius: 6px;
    font-weight: 500;
}

.el-button--primary {
    background: #409eff;
    border-color: #409eff;
}

.el-button--success {
    background: #67c23a;
    border-color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        padding: 16px;
    }
    
    .page-title h1 {
        font-size: 1.5rem;
    }
    
    .stats-row .el-col {
        margin-bottom: 16px;
    }
    
    .table-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .table-header .el-space {
        margin-top: 12px;
    }
    
    .upload-content {
        padding: 20px 10px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 12px;
    }
    
    .logo-title {
        font-size: 1.2rem;
    }
    
    .page-title h1 {
        font-size: 1.3rem;
    }
    
    .stat-item {
        padding: 16px;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
}
