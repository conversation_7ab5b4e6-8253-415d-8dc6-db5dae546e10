/* 后台管理系统样式 - admin.css */

/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f7fa;
    color: #333;
}

.admin-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-header {
    background: #409eff;
    color: white;
    padding: 0 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,.15);
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo-icon {
    margin-right: 12px;
    color: #ffd04b;
}

.logo-title {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-section {
    display: flex;
    align-items: center;
}

.admin-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.admin-sidebar {
    width: 250px;
    background: white;
    border-right: 1px solid #e4e7ed;
    overflow-y: auto;
}

.admin-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f5f7fa;
}

.content-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.content-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
}

.content-subtitle {
    color: #909399;
    font-size: 0.9rem;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.dashboard-card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border-left: 4px solid transparent;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px 0 rgba(0,0,0,.15);
}

.dashboard-card.zen-mama {
    border-left-color: #409eff;
}

.dashboard-card.tools {
    border-left-color: #67c23a;
}

.dashboard-card.reports {
    border-left-color: #e6a23c;
}

.dashboard-card.settings {
    border-left-color: #f56c6c;
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.card-icon {
    font-size: 2rem;
    margin-right: 12px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: white;
}

.zen-mama .card-icon {
    background: linear-gradient(135deg, #409eff, #3a8ee6);
}

.tools .card-icon {
    background: linear-gradient(135deg, #67c23a, #5daf34);
}

.reports .card-icon {
    background: linear-gradient(135deg, #e6a23c, #cf9236);
}

.settings .card-icon {
    background: linear-gradient(135deg, #f56c6c, #dd6161);
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #303133;
}

.card-description {
    color: #606266;
    line-height: 1.6;
    margin-bottom: 16px;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #f0f9ff;
    color: #409eff;
}

.status-beta {
    background: #f0f9ff;
    color: #909399;
}

.card-arrow {
    color: #c0c4cc;
    transition: all 0.3s ease;
}

.dashboard-card:hover .card-arrow {
    color: #409eff;
    transform: translateX(4px);
}

.stats-section {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.stats-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.stats-title i {
    margin-right: 8px;
    color: #409eff;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #ecf5ff;
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
    color: #409eff;
    margin-bottom: 12px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #303133;
    margin-bottom: 4px;
}

.stat-label {
    color: #909399;
    font-size: 0.9rem;
}

/* Element Plus 自定义样式 */
.el-dropdown-link {
    cursor: pointer;
    color: white;
    display: flex;
    align-items: center;
}

.el-menu-vertical {
    border-right: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-main {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        height: auto;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .admin-content {
        padding: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
